import React from 'react';

const Hero = ({ personalInfo }) => {
  return (
    <section id="home" className="hero">
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <h1>Hi, I'm <span className="highlight">{personalInfo.name}</span></h1>
            <h2>{personalInfo.title}</h2>
            <p>{personalInfo.tagline}</p>
            <div className="hero-buttons">
              <a href="#projects" className="btn btn-primary">View My Work</a>
              <a href="#contact" className="btn btn-secondary">Get In Touch</a>
            </div>
          </div>
          <div className="hero-image">
            <img src={personalInfo.profileImage} alt={personalInfo.name} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
