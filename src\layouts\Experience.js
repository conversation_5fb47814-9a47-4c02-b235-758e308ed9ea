import React from 'react';

const Experience = ({ experience }) => {
  return (
    <section id="experience" className="experience">
      <div className="container">
        <h2 className="section-title">Work Experience</h2>
        <div className="experience-timeline">
          {experience.map(job => (
            <div key={job.id} className="experience-item">
              <div className="experience-content">
                <h3>{job.position}</h3>
                <h4>{job.company}</h4>
                <span className="duration">{job.duration}</span>
                <p>{job.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Experience;
